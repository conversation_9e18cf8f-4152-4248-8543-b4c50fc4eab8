/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Tool } from '@langchain/core/tools';
import { LangChainMCPClient } from '../mcp/mcpClient.js';
import { logger } from '../utils/logger.js';
import { LangChainConfig } from '../config/config.js';
import { Type, Schema } from '@google/genai';
import type {
    Tool as CoreTool,
    ToolResult,
} from '@google/gemini-cli-core';
import { hackToolParams } from './hackUtils.js';

/**
 * Wrapper that adapts core tools to LangChain tools
 */
export class CoreToolWrapper extends Tool {
    coreTool;
    config;
    name;
    description;
    abortController;
    constructor(coreTool: CoreTool, config: LangChainConfig) {
        super();
        this.coreTool = coreTool;
        this.config = config;
        this.name = coreTool.name;
        this.description = coreTool.description;
        this.abortController = new AbortController();
    }
    /**
     * Execute the core tool and return the result as a string
     */
    async _call(input: string | undefined): Promise<string> {
        try {
            // Handle undefined input
            if (input === undefined) {
                throw new Error('Input is required but was undefined');
            }
            // Handle different input types
            let args;
            if (typeof input === 'string') {
                // Try to parse as JSON first
                try {
                    args = JSON.parse(input);
                }
                catch (_parseError) {
                    // If not JSON, map the string to the correct parameter name based on tool
                    logger.debug(`[CoreToolWrapper] Input is not JSON, treating as string: ${input}`);
                    args = this.mapStringToParams(input);
                }
            }
            else {
                throw new Error(`Invalid input: must be a string: ${typeof input}, ${input}`);
            }
            // Apply tool-specific parameter transformations
            args = hackToolParams(this.name, args, { workingDir: this.config.getWorkingDir() });

            // Validate parameters using core tool validation
            logger.debug(`[CoreToolWrapper] Validating tool params: ${JSON.stringify(args)}, tool name: ${this.name}, input: ${input}`);
            const validationError = this.coreTool.validateToolParams(args);
            if (validationError) {
                throw new Error(`Tool validation failed: ${validationError}`);
            }
            // Execute the core tool with proper abort signal
            const result = await this.coreTool.execute(args, this.abortController.signal);
            // Convert ToolResult to string for LangChain
            return this.formatToolResult(result);
        }
        catch (error) {
            logger.error(`[CoreToolWrapper] Error executing ${this.name}:`, error);
            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    return `Tool execution was cancelled: ${this.name}`;
                }
                else if (error.message.includes('timeout')) {
                    return `Tool execution timed out: ${this.name}`;
                }
                else if (error.message.includes('permission')) {
                    return `Permission denied for tool: ${this.name}`;
                }
                else {
                    return `Error executing ${this.name}: ${error.message}`;
                }
            }
            else {
                return `Error executing ${this.name}: ${String(error)}`;
            }
        }
    }

    /**
     * Override invoke method to handle object parameters directly
     * This is the primary method used by LangChain for tool execution
     */
    async invoke(input: Record<string, unknown> | string): Promise<string> {
        try {
            let args: Record<string, unknown>;

            if (typeof input === 'object' && input !== null) {
                // Check if this is a ToolCall object from LangChain ToolNode
                if ('name' in input && 'args' in input && typeof input.args === 'object') {
                    // This is a ToolCall object, extract the args
                    args = input.args as Record<string, unknown>;
                    logger.debug(`[CoreToolWrapper] Received ToolCall object for ${this.name}, extracted args: ${JSON.stringify(args)}`);
                } else {
                    // Direct object input - this is the main case for LangChain
                    args = input;
                    logger.debug(`[CoreToolWrapper] Received object input for ${this.name}: ${JSON.stringify(args)}`);
                }
            } else if (typeof input === 'string') {
                // String input - parse or map to parameters
                try {
                    args = JSON.parse(input);
                } catch (_parseError) {
                    args = this.mapStringToParams(input);
                }
                logger.debug(`[CoreToolWrapper] Received string input for ${this.name}, converted to: ${JSON.stringify(args)}`);
            } else {
                throw new Error('Invalid input: must be an object or string');
            }

            // Apply tool-specific parameter transformations
            args = hackToolParams(this.name, args, { workingDir: this.config.getWorkingDir() });
            logger.debug(`[CoreToolWrapper] After hackToolParams - tool: ${this.name}, args: ${JSON.stringify(args)}`);

            // Validate parameters using core tool validation
            logger.debug(`[CoreToolWrapper] Validating tool params: ${JSON.stringify(args)}, tool name: ${this.name}`);
            const validationError = this.coreTool.validateToolParams(args);
            if (validationError) {
                throw new Error(`Tool validation failed: ${validationError}`);
            }

            // Execute the core tool with proper abort signal
            const result = await this.coreTool.execute(args, this.abortController.signal);

            // Convert ToolResult to string for LangChain
            return this.formatToolResult(result);
        } catch (error) {
            if (this.config.getDebugMode()) {
                logger.error(`[CoreToolWrapper] Error executing ${this.name}:`, error);
            }
            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    return `Tool execution was cancelled: ${this.name}`;
                } else if (error.message.includes('timeout')) {
                    return `Tool execution timed out: ${this.name}`;
                } else if (error.message.includes('permission')) {
                    return `Permission denied for tool: ${this.name}`;
                } else {
                    return `Error executing ${this.name}: ${error.message}`;
                }
            } else {
                return `Error executing ${this.name}: ${String(error)}`;
            }
        }
    }

    /**
     * Map string input to correct parameter object based on core tool schema
     * This dynamically extracts parameter info from the tool's schema definition
     */
    private mapStringToParams(input: string): Record<string, string | string[]> {
        try {
            const primaryParam = this.extractPrimaryParameter();

            if (primaryParam) {
                // Check if this parameter expects an array
                if (primaryParam.isArray) {
                    logger.debug(`[CoreToolWrapper] Parameter '${primaryParam.name}' expects array, wrapping input`);
                    return { [primaryParam.name]: [input] };
                }

                logger.debug(`[CoreToolWrapper] Mapping string input to parameter '${primaryParam.name}' for tool: ${this.name}`);
                return { [primaryParam.name]: input };
            }

            // Final fallback
            logger.warning(`[CoreToolWrapper] No suitable parameter found for tool: ${this.name}, using 'input'`);
            return { input };

        } catch (error) {
            logger.error(`[CoreToolWrapper] Error extracting schema info for tool ${this.name}:`, error);
            // Fallback to generic parameter
            return { input };
        }
    }

    /**
     * Extract the primary parameter from tool schema
     * Returns the most appropriate parameter for string input mapping
     */
    private extractPrimaryParameter(): { name: string; isArray: boolean } | null {
        try {
            const schema = this.coreTool.schema;
            const parameters = schema.parameters;

            if (!parameters || typeof parameters !== 'object') {
                logger.warning(`[CoreToolWrapper] No parameters schema found for tool: ${this.name}`);
                return null;
            }

            // Strategy 1: Use the first required parameter
            if (parameters.required && Array.isArray(parameters.required) && parameters.required.length > 0) {
                const paramName = parameters.required[0];
                const isArray = this.isParameterArray(parameters, paramName);
                logger.debug(`[CoreToolWrapper] Using first required parameter '${paramName}' for tool: ${this.name}`);
                return { name: paramName, isArray };
            }

            // Strategy 2: Look for common parameter patterns
            if (parameters.properties && typeof parameters.properties === 'object') {
                const properties = parameters.properties;

                // Common parameter name patterns in order of preference
                const commonPatterns = [
                    // File/path related
                    'absolute_path', 'path', 'file_path', 'paths',
                    // Content related  
                    'pattern', 'query', 'content', 'text',
                    // Command related
                    'command', 'url',
                    // Generic
                    'input', 'value'
                ];

                for (const pattern of commonPatterns) {
                    if (properties[pattern]) {
                        const isArray = this.isParameterArray(parameters, pattern);
                        logger.debug(`[CoreToolWrapper] Using common pattern parameter '${pattern}' for tool: ${this.name}`);
                        return { name: pattern, isArray };
                    }
                }

                // Strategy 3: Use the first safe property name
                const propertyNames = Object.keys(properties);
                const safePropertyNames = propertyNames.filter(name =>
                    !['import', 'export', 'default', 'class', 'function'].includes(name)
                );

                if (safePropertyNames.length > 0) {
                    const paramName = safePropertyNames[0];
                    const isArray = this.isParameterArray(parameters, paramName);
                    logger.debug(`[CoreToolWrapper] Using first safe parameter '${paramName}' for tool: ${this.name}`);
                    return { name: paramName, isArray };
                }

                // Strategy 4: Use any first property as last resort
                if (propertyNames.length > 0) {
                    const paramName = propertyNames[0];
                    const isArray = this.isParameterArray(parameters, paramName);
                    logger.debug(`[CoreToolWrapper] Using first available parameter '${paramName}' for tool: ${this.name}`);
                    return { name: paramName, isArray };
                }
            }

            return null;
        } catch (error) {
            logger.error(`[CoreToolWrapper] Error in extractPrimaryParameter for tool ${this.name}:`, error);
            return null;
        }
    }

    /**
     * Check if a parameter expects an array type
     */
    private isParameterArray(parameters: Schema, paramName: string): boolean {
        try {
            const paramProperty = parameters.properties?.[paramName];
            if (paramProperty && typeof paramProperty === 'object') {
                return paramProperty.type === Type.ARRAY ||
                    (typeof paramProperty.items === 'object' && paramProperty.items !== null);
            }
            return false;
        } catch (error) {
            logger.debug(`[CoreToolWrapper] Error checking array type for parameter '${paramName}':`, error);
            return false;
        }
    }

    /**
     * Cancel the current tool execution
     */
    abort() {
        this.abortController.abort();
        // Create a new controller for future executions
        this.abortController = new AbortController();
    }
    /**
     * Format ToolResult for LangChain consumption
     * Prioritizes llmContent over returnDisplay to match core package behavior
     */
    formatToolResult(result: ToolResult): string {
        // First try to use llmContent (what the LLM should see)
        if (Array.isArray(result.llmContent)) {
            return result.llmContent.map(part => {
                if (typeof part === 'string')
                    return part;
                if (typeof part === 'object' && part !== null && 'text' in part) {
                    return part.text;
                }
                return JSON.stringify(part);
            }).join('\n');
        }
        else if (typeof result.llmContent === 'string') {
            return result.llmContent;
        }
        else if (result.llmContent && typeof result.llmContent === 'object') {
            // Handle binary content (images, etc.)
            if ('inlineData' in result.llmContent) {
                return `Binary content of type ${result.llmContent.inlineData?.mimeType || 'unknown'} was processed.`;
            }
            return JSON.stringify(result.llmContent);
        }

        // Fallback to returnDisplay for user-friendly messages
        if (typeof result.returnDisplay === 'string') {
            return result.returnDisplay;
        }
        else if (result.returnDisplay && typeof result.returnDisplay === 'object') {
            // Handle FileDiff case
            if ('fileDiff' in result.returnDisplay) {
                return `File modified: ${result.returnDisplay.fileName}\n\n${result.returnDisplay.fileDiff}`;
            }
        }

        // Final fallback
        return 'Tool execution completed.';
    }
}
/**
 * LangChain tool registry that manages core tools adapted for LangChain
 */
export class LangChainToolRegistry {
    config;
    coreTools = new Map();
    langChainTools = new Map();
    mcpClient;
    constructor(config: LangChainConfig) {
        this.config = config;
        this.mcpClient = new LangChainMCPClient(config);
    }
    /**
     * Register a core tool and create its LangChain wrapper
     */
    registerCoreTool(coreTool: CoreTool) {
        this.coreTools.set(coreTool.name, coreTool);
        const langChainTool = new CoreToolWrapper(coreTool, this.config);
        this.langChainTools.set(coreTool.name, langChainTool);
    }
    /**
     * Register a native LangChain tool
     */
    registerLangChainTool(tool: Tool) {
        this.langChainTools.set(tool.name, tool);
    }
    /**
     * Get all LangChain tools for use with models
     */
    getLangChainTools() {
        return Array.from(this.langChainTools.values());
    }
    /**
     * Get a specific tool by name
     */
    getTool(name: string) {
        return this.langChainTools.get(name);
    }
    /**
     * Get core tool by name
     */
    getCoreTool(name: string) {
        return this.coreTools.get(name);
    }
    /**
     * List all registered tool names
     */
    getToolNames() {
        return Array.from(this.langChainTools.keys());
    }
    /**
     * Get function declarations for tools (for Gemini API compatibility)
     */
    getFunctionDeclarations() {
        return Array.from(this.coreTools.values()).map(tool => tool.schema);
    }
    /**
     * Discover and register core tools based on configuration
     */
    async discoverTools() {
        try {
            // Import core tool classes
            const coreModule = await import('@google/gemini-cli-core');
            const { LSTool, ReadFileTool, GrepTool, GlobTool, EditTool, WriteFileTool, WebFetchTool, ReadManyFilesTool, ShellTool, MemoryTool, WebSearchTool, } = coreModule;
            const CoreConfig = coreModule.Config;
            // Create a temporary core config to use for tool instantiation
            const tempCoreConfig = new CoreConfig({
                sessionId: 'temp',
                targetDir: this.config.getTargetDir(),
                model: this.config.getModel(),
                cwd: this.config.getWorkingDir(),
                debugMode: this.config.getDebugMode(),
                approvalMode: this.config.getApprovalMode(),
            });
            // Get tool configuration
            const coreTools = this.config.getCoreTools();
            const excludeTools = this.config.getExcludeTools();
            // Define available core tools
            const availableTools = [
                { name: 'list_directory', class: LSTool },
                { name: 'read_file', class: ReadFileTool },
                { name: 'search_file_content', class: GrepTool },
                { name: 'glob', class: GlobTool },
                { name: 'replace', class: EditTool },
                { name: 'write_file', class: WriteFileTool },
                { name: 'web_fetch', class: WebFetchTool },
                { name: 'read_many_files', class: ReadManyFilesTool },
                { name: 'run_shell_command', class: ShellTool },
                { name: 'save_memory', class: MemoryTool },
                { name: 'web_search', class: WebSearchTool },
            ];
            // Filter tools based on configuration (enhanced to match core logic)
            const toolsToRegister = availableTools.filter(tool => {
                const className = tool.class.name;
                const toolName = tool.class.Name || tool.name;
                
                let isEnabled = false;
                
                // If coreTools is specified, check if tool is included
                if (coreTools === undefined) {
                    isEnabled = true;
                } else {
                    isEnabled = coreTools.some(
                        (configTool) =>
                            configTool === className ||
                            configTool === toolName ||
                            configTool.startsWith(`${className}(`) ||
                            configTool.startsWith(`${toolName}(`)
                    );
                }
                
                // excludeTools takes precedence over coreTools
                if (
                    excludeTools?.includes(className) ||
                    excludeTools?.includes(toolName)
                ) {
                    isEnabled = false;
                }
                
                return isEnabled;
            });
            // Register filtered tools with enhanced error handling
            let registeredCount = 0;
            const failedTools = [];
            for (const toolDef of toolsToRegister) {
                try {
                    const coreToolInstance = new toolDef.class(tempCoreConfig);
                    this.registerCoreTool(coreToolInstance);
                    registeredCount++;
                }
                catch (error) {
                    logger.warning(`Failed to register tool ${toolDef.name}:`, error);
                    failedTools.push(toolDef.name);
                }
            }
            logger.info(`Discovered and registered ${registeredCount} core tools`);
            if (failedTools.length > 0) {
                logger.warning(`Failed to register ${failedTools.length} tools:`, failedTools);
            }
            // Discover and register MCP tools
            await this.discoverMCPTools();
        }
        catch (error) {
            logger.error('Failed to discover tools:', error);
            // Provide more specific error messages
            if (error instanceof Error) {
                if (error.message.includes('module')) {
                    throw new Error('Failed to load core tools module. Please check your installation.');
                }
                else if (error.message.includes('config')) {
                    throw new Error('Invalid tool configuration. Please check your settings.');
                }
                else {
                    throw new Error(`Tool discovery failed: ${error.message}`);
                }
            }
            else {
                throw new Error(`Tool discovery failed: ${String(error)}`);
            }
        }
    }
    /**
     * Discover and register MCP tools
     */
    async discoverMCPTools() {
        try {
            if (!this.mcpClient.isMCPConfigured()) {
                logger.debug('[LangChainToolRegistry] No MCP servers configured, skipping MCP tool discovery');
                return;
            }
            logger.debug('[LangChainToolRegistry] Starting MCP tool discovery...');
            const mcpTools = await this.mcpClient.discoverMCPTools();
            // Register discovered MCP tools
            for (const tool of mcpTools) {
                this.registerLangChainTool(tool);
                logger.debug(`[LangChainToolRegistry] Registered MCP tool: ${tool.name}`);
            }
            logger.info(`[LangChainToolRegistry] Successfully registered ${mcpTools.length} MCP tools`);
        }
        catch (error) {
            logger.error('[LangChainToolRegistry] Failed to discover MCP tools:', error);
            // Don't throw error for MCP discovery failure - continue with core tools
        }
    }
    /**
     * Get MCP client for advanced MCP operations
     */
    getMCPClient() {
        return this.mcpClient;
    }
    /**
     * Clear all registered tools
     */
    clear() {
        this.coreTools.clear();
        this.langChainTools.clear();
        this.mcpClient.clearDiscoveredTools();
    }
    /**
     * Get tool count
     */
    getToolCount() {
        return this.langChainTools.size;
    }
}
