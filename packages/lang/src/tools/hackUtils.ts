import path from 'node:path';
import fs from 'node:fs';
import logger from '../utils/logger.js';

/**
 * Hack the list_directory tool to handle relative paths (for deepseek-v3)
 */
function hackListDirectory(
  args: Record<string, unknown>,
  { workingDir }: { workingDir: string },
): Record<string, unknown> {
  if (args && typeof args === 'object' && ('path' in args || 'input' in args)) {
    const pathValue = args.path || args.input;
    if (typeof pathValue === 'string') {
      if (!path.isAbsolute(pathValue)) {
        const absolutePath = path.resolve(workingDir, pathValue);
        args.path = absolutePath;
        logger.debug(
          `[CoreToolWrapper] Converted relative path '${pathValue}' to absolute path '${absolutePath}' for list_directory tool`,
        );
      } else {
        args.path = pathValue;
      }
    }
    // Remove the input property to avoid confusion
    if ('input' in args) {
      delete args.input;
    }
  }
  return args;
}

function hackReadFile(args: Record<string, unknown>, { workingDir }: { workingDir: string }): Record<string, unknown> {
  if (args && typeof args === 'object' && ('input' in args || 'absolute_path' in args)) {
    const inputValue = args.input || args.absolute_path;
    if (typeof inputValue === 'string') {
      if (!path.isAbsolute(inputValue)) {
        const absolutePath = path.resolve(workingDir, inputValue);
        args.absolute_path = absolutePath;
      } else {
        // Special handling for paths that look like absolute paths but might be intended as relative
        // For example, "/LICENSE" might be intended as "LICENSE" relative to workingDir
        const basename = path.basename(inputValue);
        const potentialRelativePath = path.resolve(workingDir, basename);
        
        // If the absolute path doesn't exist but a file with the same name exists in workingDir,
        // use the relative path instead
        if (!fs.existsSync(inputValue) && fs.existsSync(potentialRelativePath)) {
          logger.debug(`[hackReadFile] Converting potentially incorrect absolute path '${inputValue}' to '${potentialRelativePath}'`);
          args.absolute_path = potentialRelativePath;
        } else {
          args.absolute_path = inputValue;
        }
      }
    } else {
      args.absolute_path = inputValue;
    }
    // Remove the input property to avoid confusion
    if ('input' in args) {
      delete args.input;
    }
  }
  return args;
}

export function hackGlob(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && ('input' in args)) {
    const inputValue = args.input;
    args.pattern = inputValue;
    // Remove the input property to avoid confusion
    delete args.input;
  }
  return args;
}

function hackSearchFileContent(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    args.pattern = args.input;
    delete args.input;
  }
  return args;
}

function hackWriteFile(args: Record<string, unknown>, { workingDir }: { workingDir: string }): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    // For write_file, input might be the file path or content
    // We need to be more careful here
    if (typeof args.input === 'string' && !args.file_path && !args.content) {
      // If only input is provided, treat it as file_path
      const inputValue = args.input as string;
      if (!path.isAbsolute(inputValue)) {
        args.file_path = path.resolve(workingDir, inputValue);
      } else {
        args.file_path = inputValue;
      }
      delete args.input;
    }
  }
  return args;
}

function hackRunShellCommand(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    args.command = args.input;
    delete args.input;
  }
  return args;
}

function hackWebFetch(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    args.url = args.input;
    delete args.input;
  }
  return args;
}

function hackWebSearch(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    args.query = args.input;
    delete args.input;
  }
  return args;
}

function hackSaveMemory(args: Record<string, unknown>): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    args.content = args.input;
    delete args.input;
  }
  return args;
}

function hackReadManyFiles(args: Record<string, unknown>, { workingDir }: { workingDir: string }): Record<string, unknown> {
  if (args && typeof args === 'object' && 'input' in args) {
    const inputValue = args.input;
    if (typeof inputValue === 'string') {
      // Convert single string to array
      const filePath = path.isAbsolute(inputValue) ? inputValue : path.resolve(workingDir, inputValue);
      args.paths = [filePath];
    } else if (Array.isArray(inputValue)) {
      // Convert relative paths to absolute
      args.paths = inputValue.map(p => 
        typeof p === 'string' && !path.isAbsolute(p) ? path.resolve(workingDir, p) : p
      );
    }
    delete args.input;
  }
  return args;
}

export function hackToolParams(toolName: string, args: Record<string, unknown>, { workingDir }: { workingDir: string }): Record<string, unknown> {
  logger.debug(`[hackToolParams] Hacking tool params for tool '${toolName}'`);
  switch (toolName) {
    case 'list_directory':
      return hackListDirectory(args, { workingDir });
    case 'read_file':
      return hackReadFile(args, { workingDir });
    case 'glob':
      return hackGlob(args);
    case 'search_file_content':
      return hackSearchFileContent(args);
    case 'write_file':
      return hackWriteFile(args, { workingDir });
    case 'run_shell_command':
      return hackRunShellCommand(args);
    case 'web_fetch':
      return hackWebFetch(args);
    case 'web_search':
      return hackWebSearch(args);
    case 'save_memory':
      return hackSaveMemory(args);
    case 'read_many_files':
      return hackReadManyFiles(args, { workingDir });
    default:
      return args;
  }
}